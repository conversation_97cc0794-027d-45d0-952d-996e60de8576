/* Process Section Styles - MODERN REDESIGN */
.processSection {
  padding: 120px 0;
  background: linear-gradient(135deg, #0a0f1c 0%, #1a1f2e 25%, #2a2f3e 50%, #1e293b 75%, #0f172a 100%);
  position: relative;
  overflow: hidden;
  min-height: 100vh;
}

/* Enhanced Background Elements */
.backgroundElements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.floatingShape1 {
  position: absolute;
  top: 15%;
  left: 8%;
  width: 120px;
  height: 120px;
  background: linear-gradient(135deg, #3b82f6, #6366f1, #8b5cf6);
  border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
  opacity: 0.4;
  animation: float 8s ease-in-out infinite, morph 6s ease-in-out infinite;
  filter: blur(1px);
}

.floatingShape2 {
  position: absolute;
  top: 55%;
  right: 10%;
  width: 90px;
  height: 90px;
  background: linear-gradient(135deg, #ec4899, #f472b6, #fb7185);
  border-radius: 50% 20% 80% 40%;
  opacity: 0.3;
  animation: float 6s ease-in-out infinite reverse, morph 8s ease-in-out infinite;
  filter: blur(1px);
}

.floatingShape3 {
  position: absolute;
  bottom: 25%;
  left: 12%;
  width: 75px;
  height: 75px;
  background: linear-gradient(135deg, #10b981, #22c55e, #34d399);
  border-radius: 40% 60% 60% 40% / 60% 30% 70% 40%;
  opacity: 0.4;
  animation: float 10s ease-in-out infinite, morph 4s ease-in-out infinite;
  filter: blur(1px);
}

.pathLine {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 300px;
  height: 300px;
  border: 1px dashed rgba(59, 130, 246, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: rotate 25s linear infinite;
  opacity: 0.6;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
  position: relative;
  z-index: 10;
}

/* Enhanced Header Section */
.headerSection {
  text-align: center;
  margin-bottom: 100px;
}

.mainTitle {
  font-size: 4.5rem;
  font-weight: 900;
  margin-bottom: 24px;
  background: linear-gradient(135deg, #60a5fa 0%, #a78bfa 50%, #f472b6 100%);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientShift 4s ease-in-out infinite;
  position: relative;
  letter-spacing: -0.02em;
}

.mainTitle::after {
  content: '';
  position: absolute;
  bottom: -16px;
  left: 50%;
  transform: translateX(-50%);
  width: 320px;
  height: 6px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899);
  border-radius: 3px;
  animation: underlineExpand 2s ease-out 0.5s both;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.4);
}

.subtitle {
  font-size: 2.4rem;
  color: #f8fafc;
  font-weight: 700;
  margin-bottom: 24px;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
  letter-spacing: -0.01em;
}

.description {
  font-size: 1.4rem;
  color: #e2e8f0;
  line-height: 1.8;
  max-width: 850px;
  margin: 0 auto;
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
  font-weight: 400;
}

/* Enhanced Process Container */
.processContainer {
  position: relative;
  margin-bottom: 100px;
}

.stepsWrapper {
  display: flex;
  flex-direction: column;
  gap: 60px;
  position: relative;
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 60px;
}

/* Alternating Timeline Card Design */
.stepItem {
  position: relative;
  background: linear-gradient(145deg,
    rgba(255, 255, 255, 0.08) 0%,
    rgba(255, 255, 255, 0.04) 50%,
    rgba(255, 255, 255, 0.02) 100%);
  backdrop-filter: blur(24px);
  padding: 48px 40px 40px;
  border-radius: 28px;
  border: 1px solid rgba(255, 255, 255, 0.12);
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  overflow: hidden;
  opacity: 0.75;
  transform: scale(0.96) translateY(8px);
  width: 100%;
  max-width: 420px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.12),
    0 2px 8px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Left side cards (odd steps: 1, 3, 5) */
.stepItem:nth-child(odd) {
  align-self: flex-start;
  margin-left: 0;
  margin-right: auto;
}

/* Right side cards (even steps: 2, 4) */
.stepItem:nth-child(even) {
  align-self: flex-end;
  margin-left: auto;
  margin-right: 0;
}

.stepItem::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.03) 0%,
    rgba(139, 92, 246, 0.02) 50%,
    rgba(236, 72, 153, 0.03) 100%);
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.4s ease;
  z-index: -1;
}

.stepItem.active {
  opacity: 1;
  transform: scale(1) translateY(0);
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow:
    0 20px 60px rgba(59, 130, 246, 0.15),
    0 8px 32px rgba(0, 0, 0, 0.2),
    0 2px 8px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.stepItem.active::before {
  opacity: 1;
}

.stepItem:hover {
  transform: translateY(-12px) scale(1.03);
  border-color: rgba(59, 130, 246, 0.4);
  box-shadow:
    0 32px 80px rgba(59, 130, 246, 0.2),
    0 16px 48px rgba(0, 0, 0, 0.25),
    0 4px 16px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.stepItem:hover::before {
  opacity: 1;
}

/* Enhanced Step Number - Inside Cards */
.stepNumber {
  position: absolute;
  top: 26px;
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, #3b82f6 0%, #6366f1 50%, #8b5cf6 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.6rem;
  font-weight: 900;
  color: white;
  box-shadow:
    0 12px 32px rgba(59, 130, 246, 0.4),
    0 4px 16px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  z-index: 3;
  border: 2px solid rgba(255, 255, 255, 0.1);
}

/* Left side step numbers - positioned on the right side of left cards */
.stepItem:nth-child(odd) .stepNumber {
  right: 24px;
}

/* Right side step numbers - positioned on the left side of right cards */
.stepItem:nth-child(even) .stepNumber {
  left: 24px;
}

.numberGlow {
  position: absolute;
  top: -6px;
  left: -6px;
  right: -6px;
  bottom: -6px;
  background: inherit;
  border-radius: 50%;
  opacity: 0.4;
  animation: pulse 2.5s ease-in-out infinite;
  z-index: -1;
  filter: blur(2px);
}

/* Modern Icon Design */
.stepIcon {
  width: 88px;
  height: 88px;
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.15) 0%,
    rgba(139, 92, 246, 0.12) 50%,
    rgba(236, 72, 153, 0.15) 100%);
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.2rem;
  color: #60a5fa;
  margin: 32px auto 28px;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: 1px solid rgba(96, 165, 250, 0.2);
  box-shadow:
    0 8px 24px rgba(59, 130, 246, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.stepItem:hover .stepIcon {
  transform: scale(1.1) rotate(5deg);
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.25) 0%,
    rgba(139, 92, 246, 0.2) 50%,
    rgba(236, 72, 153, 0.25) 100%);
  border-color: rgba(96, 165, 250, 0.4);
  box-shadow:
    0 12px 32px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

/* Enhanced Typography */
.stepTitle {
  font-size: 1.6rem;
  color: #f8fafc;
  font-weight: 700;
  margin-bottom: 16px;
  text-align: center;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
  letter-spacing: -0.01em;
  line-height: 1.3;
}

.stepDescription {
  font-size: 1.05rem;
  color: #e2e8f0;
  line-height: 1.7;
  text-align: center;
  margin: 0;
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
  font-weight: 400;
}

/* Central Timeline Line */
.stepsWrapper::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 64px;
  bottom: 64px;
  width: 4px;
  background: linear-gradient(180deg,
    transparent 0%,
    rgba(59, 130, 246, 0.3) 10%,
    rgba(59, 130, 246, 0.5) 50%,
    rgba(59, 130, 246, 0.3) 90%,
    transparent 100%);
  border-radius: 2px;
  transform: translateX(-50%);
  z-index: 1;
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.2);
}

/* Connecting lines from cards to center */
.stepConnector {
  position: absolute;
  top: 50%;
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg,
    rgba(59, 130, 246, 0.6) 0%,
    rgba(59, 130, 246, 0.8) 100%);
  transform: translateY(-50%);
  border-radius: 2px;
  opacity: 0.8;
  z-index: 2;
}

/* Left side connectors - extend from right edge of card to center */
.stepItem:nth-child(odd) .stepConnector {
  right: -60px;
  background: linear-gradient(90deg,
    rgba(59, 130, 246, 0.6) 0%,
    rgba(59, 130, 246, 0.8) 100%);
}

/* Right side connectors - extend from left edge of card to center */
.stepItem:nth-child(even) .stepConnector {
  left: -60px;
  background: linear-gradient(90deg,
    rgba(59, 130, 246, 0.8) 0%,
    rgba(59, 130, 246, 0.6) 100%);
}

.stepItem:last-child .stepConnector {
  display: none;
}

/* Central Progress Line */
.progressLine {
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 6px;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 3px;
  transform: translateX(-50%);
  z-index: 2;
  box-shadow: inset 2px 0 4px rgba(0, 0, 0, 0.1);
}

.progressFill {
  width: 100%;
  background: linear-gradient(180deg, #3b82f6 0%, #6366f1 50%, #8b5cf6 100%);
  border-radius: 3px;
  transition: height 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  box-shadow: 0 0 16px rgba(59, 130, 246, 0.4);
}

.progressFill::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  width: 16px;
  height: 16px;
  background: linear-gradient(135deg, #60a5fa, #3b82f6);
  border-radius: 50%;
  transform: translateX(-50%);
  box-shadow:
    0 0 24px rgba(96, 165, 250, 0.8),
    0 2px 8px rgba(59, 130, 246, 0.4);
  animation: pulse 2s ease-in-out infinite;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

/* Enhanced CTA Section */
.ctaSection {
  text-align: center;
  margin-top: 20px;
}

.ctaButton {
  background: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%);
  color: white;
  border: none;
  padding: 24px 48px;
  border-radius: 60px;
  font-size: 1.4rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  display: inline-flex;
  align-items: center;
  gap: 16px;
  box-shadow:
    0 12px 40px rgba(16, 185, 129, 0.4),
    0 4px 16px rgba(16, 185, 129, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
  letter-spacing: 0.02em;
}

.ctaButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.15) 50%,
    transparent 100%);
  transition: left 0.6s ease;
}

.ctaButton:hover::before {
  left: 100%;
}

.ctaButton:hover {
  transform: translateY(-8px) scale(1.05);
  box-shadow:
    0 20px 60px rgba(16, 185, 129, 0.5),
    0 8px 24px rgba(16, 185, 129, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.2);
}

.ctaButton i {
  font-size: 1.5rem;
  transition: transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.ctaButton:hover i {
  transform: translateX(6px) rotate(15deg);
}

.buttonRipple {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.25);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.8s ease, height 0.8s ease;
}

.ctaButton:active .buttonRipple {
  width: 400px;
  height: 400px;
}

/* Enhanced Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-25px) rotate(2deg);
  }
}

@keyframes morph {
  0%, 100% {
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
    transform: scale(1);
  }
  25% {
    border-radius: 58% 42% 75% 25% / 76% 46% 54% 24%;
    transform: scale(1.05);
  }
  50% {
    border-radius: 50% 50% 33% 67% / 55% 27% 73% 45%;
    transform: scale(0.95);
  }
  75% {
    border-radius: 33% 67% 58% 42% / 63% 68% 32% 37%;
    transform: scale(1.02);
  }
}

@keyframes rotate {
  from {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes underlineExpand {
  from {
    width: 0;
    opacity: 0;
    transform: translateX(-50%) scaleX(0);
  }
  to {
    width: 320px;
    opacity: 1;
    transform: translateX(-50%) scaleX(1);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.4;
  }
  50% {
    transform: scale(1.15);
    opacity: 0.7;
  }
}

.fadeInUp {
  animation: fadeInUp 1.2s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(60px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slideInUp {
  animation: slideInUp 1s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(80px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Enhanced Responsive Design - Awesome Multi-Device Layout */

/* Extra Large Screens (1400px+) - Premium Layout */
@media (min-width: 1400px) {
  .container {
    max-width: 1600px;
    padding: 0 40px;
  }

  .stepsWrapper {
    max-width: 1200px;
    gap: 80px;
    padding: 0 80px;
  }

  .stepItem {
    max-width: 480px;
    padding: 56px 48px 48px;
  }

  .stepNumber {
    width: 72px;
    height: 72px;
    font-size: 1.8rem;
  }

  .stepIcon {
    width: 96px;
    height: 96px;
    font-size: 2.4rem;
    margin: 36px auto 32px;
  }

  .stepTitle {
    font-size: 1.8rem;
    margin-bottom: 20px;
  }

  .stepDescription {
    font-size: 1.15rem;
    line-height: 1.8;
  }
}

/* Large Screens (1200px-1399px) - Enhanced Layout */
@media (max-width: 1399px) and (min-width: 1200px) {
  .container {
    padding: 0 36px;
  }

  .stepsWrapper {
    max-width: 1000px;
    gap: 65px;
    padding: 0 60px;
  }

  .stepItem {
    max-width: 420px;
    padding: 52px 44px 44px;
  }

  .stepConnector {
    width: 70px;
  }

  .stepItem:nth-child(odd) .stepConnector {
    right: -70px;
  }

  .stepItem:nth-child(even) .stepConnector {
    left: -70px;
  }
}

/* Medium-Large Screens (1024px-1199px) - Tablet Landscape */
@media (max-width: 1199px) and (min-width: 1024px) {
  .container {
    padding: 0 32px;
  }

  .mainTitle {
    font-size: 3.8rem;
  }

  .mainTitle::after {
    width: 280px;
    height: 5px;
  }

  .subtitle {
    font-size: 2.1rem;
  }

  .stepsWrapper {
    max-width: 900px;
    gap: 55px;
    padding: 0 50px;
  }

  .stepItem {
    max-width: 380px;
    padding: 48px 40px 40px;
  }

  .stepConnector {
    width: 65px;
  }

  .stepItem:nth-child(odd) .stepConnector {
    right: -65px;
  }

  .stepItem:nth-child(even) .stepConnector {
    left: -65px;
  }

  .floatingShape1,
  .floatingShape2,
  .floatingShape3 {
    opacity: 0.25;
  }

  .pathLine {
    width: 250px;
    height: 250px;
    opacity: 0.4;
  }
}

/* Tablet Portrait (768px-1023px) - Transition Layout */
@media (max-width: 1023px) and (min-width: 768px) {
  .processSection {
    padding: 100px 0;
  }

  .container {
    padding: 0 28px;
  }

  .headerSection {
    margin-bottom: 90px;
  }

  .mainTitle {
    font-size: 3.4rem;
    margin-bottom: 22px;
  }

  .mainTitle::after {
    width: 260px;
    height: 4px;
  }

  .subtitle {
    font-size: 2rem;
    margin-bottom: 22px;
  }

  .description {
    font-size: 1.25rem;
    max-width: 650px;
  }

  /* Hybrid layout - slightly reduced alternating */
  .stepsWrapper {
    max-width: 750px;
    gap: 45px;
    padding: 0 35px;
  }

  .stepItem {
    max-width: 320px;
    padding: 44px 36px 36px;
  }

  .stepItem:nth-child(odd) {
    margin-left: 20px;
  }

  .stepItem:nth-child(even) {
    margin-right: 20px;
  }

  .stepConnector {
    width: 55px;
  }

  .stepItem:nth-child(odd) .stepConnector {
    right: -55px;
  }

  .stepItem:nth-child(even) .stepConnector {
    left: -55px;
  }

  .stepNumber {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }

  .stepIcon {
    width: 80px;
    height: 80px;
    font-size: 2.1rem;
    margin: 30px auto 26px;
  }

  .stepTitle {
    font-size: 1.55rem;
    margin-bottom: 15px;
  }

  .stepDescription {
    font-size: 1.08rem;
    line-height: 1.65;
  }

  .floatingShape1,
  .floatingShape2,
  .floatingShape3 {
    opacity: 0.15;
  }

  .pathLine {
    width: 200px;
    height: 200px;
    opacity: 0.3;
  }
}

/* Mobile Landscape & Small Tablets (667px-767px) - Optimized Layout */
@media (max-width: 767px) and (min-width: 667px) {
  .processSection {
    padding: 90px 0;
  }

  .container {
    padding: 0 24px;
  }

  .headerSection {
    margin-bottom: 75px;
  }

  .mainTitle {
    font-size: 3rem;
    margin-bottom: 20px;
  }

  .mainTitle::after {
    width: 220px;
    height: 4px;
  }

  .subtitle {
    font-size: 1.8rem;
    margin-bottom: 20px;
  }

  .description {
    font-size: 1.15rem;
    max-width: 600px;
  }

  /* Centered single-column layout with enhanced spacing */
  .stepsWrapper {
    max-width: 100%;
    gap: 40px;
    padding: 0 20px;
    align-items: center;
    position: relative;
  }

  .stepItem {
    padding: 32px 34px 32px; /* Reduced top padding since no step number */
    max-width: 100%;
    margin: 0 auto !important;
    align-self: center !important;
    border-radius: 26px;
    position: relative;
    overflow: hidden;
    z-index: 10; /* Ensure cards are above any background elements */
  }

  /* Add subtle slide-in animation for mobile */
  .stepItem::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
      transparent 0%,
      rgba(96, 165, 250, 0.1) 50%,
      transparent 100%);
    transition: left 0.8s ease;
    z-index: 1;
  }

  .stepItem:hover::after {
    left: 100%;
  }

  .stepNumber {
    display: none !important;
  }

  .stepIcon {
    width: 78px;
    height: 78px;
    font-size: 2.05rem;
    margin: 30px auto 26px;
    border-radius: 22px;
    z-index: 12;
    position: relative;
  }

  .stepTitle {
    font-size: 1.52rem;
    margin-bottom: 15px;
    z-index: 12;
    position: relative;
  }

  .stepDescription {
    font-size: 1.05rem;
    line-height: 1.65;
    z-index: 12;
    position: relative;
  }

  .ctaButton {
    padding: 22px 42px;
    font-size: 1.25rem;
    gap: 15px;
  }

  /* Completely hide timeline elements on mobile */
  .stepConnector {
    display: none !important;
  }

  .stepsWrapper::before {
    display: none !important;
  }

  .progressLine {
    display: none !important;
  }

  .floatingShape1,
  .floatingShape2,
  .floatingShape3,
  .pathLine {
    display: none;
  }
}

/* Mobile Portrait (480px-666px) - Compact Awesome Layout */
@media (max-width: 666px) and (min-width: 480px) {
  .processSection {
    padding: 80px 0;
  }

  .container {
    padding: 0 20px;
  }

  .headerSection {
    margin-bottom: 70px;
  }

  .mainTitle {
    font-size: 2.8rem;
    margin-bottom: 18px;
    line-height: 1.1;
  }

  .mainTitle::after {
    width: 200px;
    height: 3px;
    bottom: -14px;
  }

  .subtitle {
    font-size: 1.7rem;
    margin-bottom: 18px;
  }

  .description {
    font-size: 1.12rem;
    max-width: 500px;
    line-height: 1.7;
  }

  /* Enhanced mobile card layout */
  .stepsWrapper {
    max-width: 100%;
    gap: 35px;
    padding: 0 18px;
    align-items: center;
    position: relative;
  }

  .stepItem {
    padding: 28px 30px 28px; /* Reduced top padding since no step number */
    max-width: 100%;
    margin: 0 auto !important;
    align-self: center !important;
    border-radius: 24px;
    transform: scale(0.98);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    z-index: 10;
  }

  .stepItem:hover {
    transform: scale(1.02) translateY(-4px);
  }

  .stepNumber {
    display: none !important;
  }

  .stepIcon {
    width: 74px;
    height: 74px;
    font-size: 1.95rem;
    margin: 28px auto 24px;
    border-radius: 20px;
    position: relative;
    z-index: 12;
  }

  .stepTitle {
    font-size: 1.45rem;
    margin-bottom: 14px;
    line-height: 1.3;
    position: relative;
    z-index: 12;
  }

  .stepDescription {
    font-size: 1.02rem;
    line-height: 1.6;
    position: relative;
    z-index: 12;
  }

  .ctaButton {
    padding: 20px 38px;
    font-size: 1.18rem;
    gap: 14px;
  }

  /* Completely hide all timeline elements on mobile */
  .stepsWrapper::before {
    display: none !important;
  }

  .progressLine {
    display: none !important;
  }

  .stepConnector {
    display: none !important;
  }

  .floatingShape1,
  .floatingShape2,
  .floatingShape3,
  .pathLine {
    display: none;
  }
}

/* Small Mobile (320px-479px) - Ultra-Compact Awesome Design */
@media (max-width: 479px) {
  .processSection {
    padding: 70px 0;
    background: linear-gradient(135deg, #0a0f1c 0%, #1a1f2e 30%, #2a2f3e 60%, #1e293b 90%, #0f172a 100%);
  }

  .container {
    padding: 0 16px;
  }

  .headerSection {
    margin-bottom: 55px;
  }

  .mainTitle {
    font-size: 2.4rem;
    margin-bottom: 16px;
    line-height: 1.1;
    letter-spacing: -0.01em;
  }

  .mainTitle::after {
    width: 160px;
    height: 3px;
    bottom: -12px;
  }

  .subtitle {
    font-size: 1.5rem;
    margin-bottom: 16px;
    line-height: 1.2;
  }

  .description {
    font-size: 1.08rem;
    line-height: 1.7;
    max-width: 400px;
  }

  .processContainer {
    margin-bottom: 55px;
  }

  /* Ultra-compact card design */
  .stepsWrapper {
    gap: 24px;
    padding: 0 14px;
    max-width: 100%;
    position: relative;
  }

  .stepItem {
    padding: 24px 20px 24px; /* Reduced top padding since no step number */
    border-radius: 22px;
    position: relative;
    background: linear-gradient(145deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.05) 50%,
      rgba(255, 255, 255, 0.02) 100%);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    transform: scale(0.97);
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    z-index: 10;
    margin: 0 auto !important;
    align-self: center !important;
    max-width: 100%;
  }

  .stepItem:hover {
    transform: scale(1) translateY(-2px);
    border-color: rgba(96, 165, 250, 0.3);
    box-shadow:
      0 16px 40px rgba(59, 130, 246, 0.15),
      0 6px 20px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.15);
  }

  /* Hide step numbers on small mobile */
  .stepNumber {
    display: none !important;
  }

  /* Compact icons */
  .stepIcon {
    width: 64px;
    height: 64px;
    font-size: 1.7rem;
    margin: 22px auto 18px;
    border-radius: 16px;
    background: linear-gradient(135deg,
      rgba(59, 130, 246, 0.12) 0%,
      rgba(139, 92, 246, 0.1) 50%,
      rgba(236, 72, 153, 0.12) 100%);
    position: relative;
    z-index: 12;
  }

  /* Compact typography */
  .stepTitle {
    font-size: 1.32rem;
    margin-bottom: 11px;
    line-height: 1.25;
    font-weight: 700;
    position: relative;
    z-index: 12;
  }

  .stepDescription {
    font-size: 0.98rem;
    line-height: 1.6;
    color: #e2e8f0;
    position: relative;
    z-index: 12;
  }

  /* Compact CTA button */
  .ctaButton {
    padding: 16px 32px;
    font-size: 1.05rem;
    gap: 11px;
    border-radius: 48px;
    font-weight: 700;
  }

  .ctaButton i {
    font-size: 1.2rem;
  }

  /* Completely hide all timeline elements on small mobile */
  .stepsWrapper::before {
    display: none !important;
  }

  .progressLine {
    display: none !important;
  }

  .stepConnector {
    display: none !important;
  }

  .floatingShape1,
  .floatingShape2,
  .floatingShape3,
  .pathLine {
    display: none;
  }
}

/* Extra Small Mobile (280px-319px) - Minimal Awesome Design */
@media (max-width: 319px) {
  .processSection {
    padding: 60px 0;
  }

  .container {
    padding: 0 12px;
  }

  .headerSection {
    margin-bottom: 45px;
  }

  .mainTitle {
    font-size: 2.1rem;
    margin-bottom: 14px;
    line-height: 1.1;
  }

  .mainTitle::after {
    width: 140px;
    height: 2px;
    bottom: -10px;
  }

  .subtitle {
    font-size: 1.35rem;
    margin-bottom: 14px;
  }

  .description {
    font-size: 1.02rem;
    line-height: 1.65;
  }

  .processContainer {
    margin-bottom: 45px;
  }

  .stepsWrapper {
    gap: 20px;
    padding: 0 12px;
    position: relative;
  }

  .stepItem {
    padding: 22px 16px 22px; /* Reduced top padding since no step number */
    border-radius: 20px;
    position: relative;
    z-index: 10;
    margin: 0 auto !important;
    align-self: center !important;
    max-width: 100%;
  }

  .stepNumber {
    display: none !important;
  }

  .stepIcon {
    width: 58px;
    height: 58px;
    font-size: 1.5rem;
    margin: 20px auto 16px;
    border-radius: 14px;
    position: relative;
    z-index: 12;
  }

  .stepTitle {
    font-size: 1.22rem;
    margin-bottom: 10px;
    line-height: 1.2;
    position: relative;
    z-index: 12;
  }

  .stepDescription {
    font-size: 0.94rem;
    line-height: 1.55;
    position: relative;
    z-index: 12;
  }

  .ctaButton {
    padding: 14px 28px;
    font-size: 1rem;
    gap: 10px;
    border-radius: 44px;
  }

  .ctaButton i {
    font-size: 1.1rem;
  }

  /* Ensure no timeline elements appear */
  .stepsWrapper::before {
    display: none !important;
  }

  .progressLine {
    display: none !important;
  }

  .stepConnector {
    display: none !important;
  }
}

/* ===== AWESOME RESPONSIVE ENHANCEMENTS ===== */

/* Enhanced Mobile Interactions */
@media (max-width: 768px) {
  /* Add touch-friendly hover states */
  .stepItem {
    cursor: pointer;
    -webkit-tap-highlight-color: transparent;
  }

  .stepItem:active {
    transform: scale(0.98) translateY(2px);
  }

  /* Enhanced mobile animations */
  .stepItem.active {
    animation: mobileStepPulse 2s ease-in-out infinite;
  }

  /* Mobile-specific step reveal animation */
  .stepItem.slideInUp {
    animation: mobileSlideInUp 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
  }

  /* Progressive step highlighting on mobile */
  .stepItem:nth-child(1) { animation-delay: 0.1s; }
  .stepItem:nth-child(2) { animation-delay: 0.2s; }
  .stepItem:nth-child(3) { animation-delay: 0.3s; }
  .stepItem:nth-child(4) { animation-delay: 0.4s; }
  .stepItem:nth-child(5) { animation-delay: 0.5s; }
}

/* Tablet-specific enhancements */
@media (min-width: 768px) and (max-width: 1023px) {
  /* Smooth alternating reveal */
  .stepItem:nth-child(odd).slideInUp {
    animation: slideInFromLeft 1s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
  }

  .stepItem:nth-child(even).slideInUp {
    animation: slideInFromRight 1s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
  }

  /* Enhanced tablet hover effects */
  .stepItem:hover {
    transform: translateY(-8px) scale(1.02);
  }

  .stepItem:hover .stepIcon {
    transform: scale(1.08) rotate(3deg);
  }
}

/* Desktop enhancements */
@media (min-width: 1024px) {
  /* Enhanced alternating animations */
  .stepItem:nth-child(odd).slideInUp {
    animation: slideInFromLeft 1.2s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
  }

  .stepItem:nth-child(even).slideInUp {
    animation: slideInFromRight 1.2s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
  }

  /* Enhanced desktop hover effects */
  .stepItem:hover {
    transform: translateY(-12px) scale(1.03);
  }

  .stepItem:hover .stepNumber {
    transform: scale(1.1);
    box-shadow:
      0 16px 40px rgba(59, 130, 246, 0.5),
      0 6px 20px rgba(59, 130, 246, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.25);
  }

  .stepItem:hover .stepIcon {
    transform: scale(1.12) rotate(5deg);
  }

  /* Connector glow effect on hover */
  .stepItem:hover .stepConnector {
    background: linear-gradient(90deg,
      rgba(96, 165, 250, 0.8) 0%,
      rgba(96, 165, 250, 1) 100%);
    box-shadow: 0 0 20px rgba(96, 165, 250, 0.6);
    height: 4px;
  }
}

/* ===== NEW AWESOME ANIMATIONS ===== */

/* Mobile step pulse animation */
@keyframes mobileStepPulse {
  0%, 100% {
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.12),
      0 2px 8px rgba(0, 0, 0, 0.08),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }
  50% {
    box-shadow:
      0 12px 40px rgba(59, 130, 246, 0.2),
      0 4px 16px rgba(59, 130, 246, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.15);
  }
}

/* Mobile slide in animation */
@keyframes mobileSlideInUp {
  from {
    opacity: 0;
    transform: translateY(40px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Slide in from left (odd steps) */
@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-60px) translateY(20px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateX(0) translateY(0) scale(1);
  }
}

/* Slide in from right (even steps) */
@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(60px) translateY(20px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateX(0) translateY(0) scale(1);
  }
}

/* Enhanced progress line animation */
@keyframes progressGlow {
  0%, 100% {
    box-shadow: 0 0 16px rgba(59, 130, 246, 0.4);
  }
  50% {
    box-shadow: 0 0 24px rgba(59, 130, 246, 0.6);
  }
}

.progressFill {
  animation: progressGlow 3s ease-in-out infinite;
}

/* ===== RESPONSIVE GRID ENHANCEMENTS ===== */

/* Large screens - 3-column grid option for steps */
@media (min-width: 1400px) {
  .stepsWrapper.gridLayout {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
    max-width: 1200px;
  }

  .stepsWrapper.gridLayout .stepItem {
    margin: 0 !important;
    align-self: stretch !important;
  }

  .stepsWrapper.gridLayout .stepItem:nth-child(4),
  .stepsWrapper.gridLayout .stepItem:nth-child(5) {
    grid-column: span 1;
  }

  /* Center the last row items */
  .stepsWrapper.gridLayout .stepItem:nth-child(4) {
    grid-column: 1 / 2;
    margin-left: auto;
  }

  .stepsWrapper.gridLayout .stepItem:nth-child(5) {
    grid-column: 3 / 4;
    margin-right: auto;
  }
}

/* Medium screens - 2-column grid option */
@media (min-width: 768px) and (max-width: 1399px) {
  .stepsWrapper.gridLayout {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 35px;
    max-width: 800px;
  }

  .stepsWrapper.gridLayout .stepItem {
    margin: 0 !important;
    align-self: stretch !important;
  }

  /* Center the last item */
  .stepsWrapper.gridLayout .stepItem:nth-child(5) {
    grid-column: 1 / -1;
    max-width: 400px;
    justify-self: center;
  }
}

/* ===== MOBILE-SPECIFIC ENHANCEMENTS ===== */

/* Mobile progress dots */
.mobileProgress {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.progressDots {
  display: flex;
  gap: 8px;
  align-items: center;
}

.progressDot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.progressDot.activeDot {
  background: linear-gradient(135deg, #60a5fa, #3b82f6);
  transform: scale(1.2);
  box-shadow: 0 0 12px rgba(96, 165, 250, 0.6);
}

/* Mobile navigation controls */
.mobileNavigation {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  margin-top: 30px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 50px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.navButton {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  border: none;
  background: linear-gradient(135deg, #3b82f6, #6366f1);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.1rem;
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
}

.navButton:hover:not(:disabled) {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.navButton:active:not(:disabled) {
  transform: scale(0.95);
}

.navButton:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  transform: none;
}

.stepCounter {
  font-size: 1.1rem;
  font-weight: 600;
  color: #f8fafc;
  min-width: 80px;
  text-align: center;
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
}

/* Enhanced touch interactions */
@media (max-width: 768px) {
  .stepItem {
    touch-action: manipulation;
    user-select: none;
    -webkit-user-select: none;
    -webkit-touch-callout: none;
  }

  .stepItem:focus {
    outline: 2px solid rgba(96, 165, 250, 0.6);
    outline-offset: 4px;
  }

  .stepItem:focus-visible {
    outline: 2px solid rgba(96, 165, 250, 0.8);
  }

  /* Hide mobile navigation on very small screens */
  @media (max-width: 380px) {
    .mobileNavigation {
      gap: 15px;
      padding: 15px;
      margin-top: 25px;
    }

    .navButton {
      width: 40px;
      height: 40px;
      font-size: 1rem;
    }

    .stepCounter {
      font-size: 1rem;
      min-width: 70px;
    }
  }
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */

/* High contrast mode support */
@media (prefers-contrast: high) {
  .stepItem {
    border: 2px solid rgba(255, 255, 255, 0.8);
  }

  .stepItem.active {
    border-color: #60a5fa;
  }

  .stepNumber {
    border: 2px solid rgba(255, 255, 255, 0.5);
  }

  .progressDot.activeDot {
    border: 2px solid #ffffff;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .stepItem,
  .stepIcon,
  .stepNumber,
  .progressFill,
  .floatingShape1,
  .floatingShape2,
  .floatingShape3,
  .pathLine {
    animation: none !important;
    transition: none !important;
  }

  .stepItem:hover {
    transform: none;
  }

  .ctaButton:hover {
    transform: none;
  }
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  .processSection {
    background: linear-gradient(135deg, #000000 0%, #1a1a1a 25%, #2a2a2a 50%, #1e1e1e 75%, #0a0a0a 100%);
  }

  .stepItem {
    background: linear-gradient(145deg,
      rgba(255, 255, 255, 0.12) 0%,
      rgba(255, 255, 255, 0.06) 50%,
      rgba(255, 255, 255, 0.03) 100%);
    border-color: rgba(255, 255, 255, 0.15);
  }

  .stepItem.active {
    border-color: rgba(96, 165, 250, 0.4);
  }
}
