@import "tailwindcss";

/* Font Awesome Icons */
@font-face {
  font-family: 'Font Awesome 6 Free';
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url('/assets/fonts/fa-solid-900.woff2') format('woff2'),
       url('/assets/fonts/fa-solid-900.ttf') format('truetype');
}

@font-face {
  font-family: 'Font Awesome 6 Free';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url('/assets/fonts/fa-regular-400.woff2') format('woff2'),
       url('/assets/fonts/fa-regular-400.ttf') format('truetype');
}

@font-face {
  font-family: 'Font Awesome 6 Brands';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url('/assets/fonts/fa-brands-400.woff2') format('woff2'),
       url('/assets/fonts/fa-brands-400.ttf') format('truetype');
}

.fa-solid {
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
}

.fa-regular {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}

.fa-brands {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}

/* Font Awesome icon base styles */
.fa-solid:before,
.fa-regular:before {
  display: inline-block;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
}

/* Specific icon content */
.fa-bars:before { content: "\f0c9"; }
.fa-times:before { content: "\f00d"; }
.fa-chevron-down:before { content: "\f078"; }
.fa-phone:before { content: "\f095"; }
.fa-arrow-right:before { content: "\f061"; }
.fa-check:before { content: "\f00c"; }
.fa-graduation-cap:before { content: "\f19d"; }

/* ProcessSection icons */
.fa-comments:before { content: "\f086"; }
.fa-university:before { content: "\f19c"; }
.fa-file-alt:before { content: "\f15c"; }
.fa-passport:before { content: "\f5ab"; }
.fa-hands-helping:before { content: "\f4c4"; }
.fa-rocket:before { content: "\f135"; }

/* ExpertGuidanceSection icons */
.fa-user-graduate:before { content: "\f501"; }
.fa-bullseye:before { content: "\f140"; }
.fa-gift:before { content: "\f06b"; }

/* AboutHero icons */
.fa-heart:before { content: "\f004"; }
.fa-hands-helping:before { content: "\f4c4"; }

/* OurApproach icons */
.fa-globe:before { content: "\f0ac"; }
.fa-users:before { content: "\f0c0"; }

/* WhyChooseIreland icons */
.fa-location-dot:before { content: "\f3c5"; }

/* FeaturedIrelandUniversities icons */
.fa-book:before { content: "\f02d"; }
.fa-euro-sign:before { content: "\f153"; }

/* PartnerIrelandUniversities icons */
.fa-globe-europe:before { content: "\f7a2"; }
.fa-comments:before { content: "\f086"; }
.fa-calendar-check:before { content: "\f274"; }
.fa-user-tie:before { content: "\f508"; }
.fa-file-alt:before { content: "\f15c"; }

/* CanadaUniversitiesHero icons */
.fa-maple-leaf:before { content: "\f6c6"; }
.fa-passport:before { content: "\f5ab"; }
.fa-globe-americas:before { content: "\f57d"; }
.fa-chevron-right:before { content: "\f054"; }
.fa-play:before { content: "\f04b"; }
.fa-clock:before { content: "\f017"; }
.fa-calendar-alt:before { content: "\f073"; }
.fa-dollar-sign:before { content: "\f155"; }
.fa-language:before { content: "\f1ab"; }

/* WhyChooseCanada icons */
.fa-shield-alt:before { content: "\f3ed"; }

/* GlobalVision icons */
.fa-globe-americas:before { content: "\f57d"; }
.fa-rocket:before { content: "\f135"; }
.fa-graduation-cap:before { content: "\f19d"; }
.fa-university:before { content: "\f19c"; }
.fa-map-marker-alt:before { content: "\f3c5"; }
.fa-earth-americas:before { content: "\f57d"; }
.fa-users-line:before { content: "\e592"; }
.fa-building-columns:before { content: "\f19c"; }

/* OurPassion icons */
.fa-file-alt:before { content: "\f15c"; }
.fa-passport:before { content: "\f5ab"; }
.fa-coins:before { content: "\f51e"; }
.fa-home:before { content: "\f015"; }
.fa-calendar-check:before { content: "\f274"; }
.fa-phone:before { content: "\f095"; }

/* AboutEducation icons */
.fa-book-open:before { content: "\f518"; }
.fa-file-signature:before { content: "\f573"; }
.fa-arrow-right:before { content: "\f061"; }
.fa-star:before { content: "\f005"; }
.fa-trophy:before { content: "\f091"; }

/* WhoWeAre icons */
.fa-calendar-alt:before { content: "\f073"; }
.fa-globe:before { content: "\f0ac"; }
.fa-user-graduate:before { content: "\f501"; }
.fa-lightbulb:before { content: "\f0eb"; }
.fa-handshake:before { content: "\f2b5"; }
.fa-award:before { content: "\f559"; }

/* OurPurpose icons */
.fa-bullseye:before { content: "\f140"; }
.fa-compass:before { content: "\f14e"; }
.fa-eye:before { content: "\f06e"; }
.fa-link:before { content: "\f0c1"; }

/* StudentSuccessSection icons */
.fa-star:before { content: "\f005"; }
.fa-quote-left:before { content: "\f10d"; }
.fa-quote-right:before { content: "\f10e"; }
.fa-check-circle:before { content: "\f058"; }

/* Footer icons */
.fa-building:before { content: "\f1ad"; }
.fa-briefcase:before { content: "\f0b1"; }
.fa-phone:before { content: "\f095"; }
.fa-envelope:before { content: "\f0e0"; }
.fa-chevron-up:before { content: "\f077"; }

/* Social Media Brand icons */
.fa-youtube:before { content: "\f167"; }
.fa-facebook:before { content: "\f09a"; }
.fa-x-twitter:before { content: "\e61b"; }
.fa-linkedin:before { content: "\f08c"; }

/* WhatGuidesUs icons */
.fa-compass:before { content: "\f14e"; }
.fa-user-graduate:before { content: "\f501"; }
.fa-award:before { content: "\f559"; }
.fa-lightbulb:before { content: "\f0eb"; }
.fa-globe:before { content: "\f0ac"; }

/* WhyChooseUs icons */
.fa-star:before { content: "\f005"; }
.fa-user-tie:before { content: "\f508"; }
.fa-handshake:before { content: "\f2b5"; }
.fa-hands-helping:before { content: "\f4c4"; }
.fa-trophy:before { content: "\f091"; }
.fa-arrow-right:before { content: "\f061"; }

/* ServicesHero icons */
.fa-briefcase:before { content: "\f0b1"; }
.fa-graduation-cap:before { content: "\f19d"; }
.fa-university:before { content: "\f19c"; }
.fa-users:before { content: "\f0c0"; }
.fa-chart-line:before { content: "\f201"; }
.fa-search:before { content: "\f002"; }
.fa-file-alt:before { content: "\f15c"; }
.fa-passport:before { content: "\f5ab"; }
.fa-plane:before { content: "\f072"; }
.fa-book:before { content: "\f02d"; }
.fa-calendar:before { content: "\f073"; }

/* PrivacyPolicy icons */
.fa-info-circle:before { content: "\f05a"; }
.fa-cog:before { content: "\f013"; }
.fa-wrench:before { content: "\f0ad"; }
.fa-balance-scale:before { content: "\f24e"; }
.fa-file-text:before { content: "\f15c"; }
.fa-circle:before { content: "\f111"; }
.fa-lock:before { content: "\f023"; }
.fa-list:before { content: "\f03a"; }
.fa-globe:before { content: "\f0ac"; }
.fa-external-link:before { content: "\f08e"; }
.fa-user:before { content: "\f007"; }
.fa-child:before { content: "\f1ae"; }
.fa-clock:before { content: "\f017"; }
.fa-pencil:before { content: "\f303"; }
.fa-envelope:before { content: "\f0e0"; }



:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

/* Smooth scrolling for the entire website */
html {
  scroll-behavior: smooth;
  scroll-padding-top: 80px; /* Account for fixed navbar */
}

/* Enhanced smooth scrolling for all browsers */
* {
  scroll-behavior: smooth;
}

/* Optimize scrolling performance */
body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
  /* Improve scrolling performance */
  -webkit-overflow-scrolling: touch;
  overflow-x: hidden;
  /* Smooth text rendering */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* ===== SMOOTH SCROLLING ENHANCEMENTS ===== */

/* Smooth scrolling for anchor links and navigation */
a[href^="#"] {
  scroll-behavior: smooth;
}

/* Enhanced scrolling for specific elements */
.smooth-scroll {
  scroll-behavior: smooth;
}

/* Optimize scrolling for mobile devices */
@media (max-width: 768px) {
  html {
    scroll-padding-top: 70px; /* Adjust for mobile navbar height */
  }

  body {
    /* Better touch scrolling on mobile */
    -webkit-overflow-scrolling: touch;
    overscroll-behavior-y: contain;
  }
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  html,
  *,
  *::before,
  *::after {
    scroll-behavior: auto !important;
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Smooth scrolling for custom scrollbars */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #3b82f6, #6366f1);
  border-radius: 4px;
  transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #2563eb, #4f46e5);
}

/* Firefox scrollbar styling */
html {
  scrollbar-width: thin;
  scrollbar-color: #3b82f6 rgba(0, 0, 0, 0.1);
}

/* ===== PERFORMANCE OPTIMIZATIONS FOR SMOOTH SCROLLING ===== */

/* GPU acceleration for better performance */
body,
html {
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  will-change: scroll-position;
}

/* Optimize rendering for smooth scrolling */
* {
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-perspective: 1000;
  perspective: 1000;
}

/* Smooth momentum scrolling on iOS */
body {
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;
}

/* Optimize scroll performance for sections */
section {
  contain: layout style paint;
  will-change: transform;
}

/* Better scroll performance for images */
img {
  will-change: transform;
  transform: translateZ(0);
}

/* Optimize animations during scroll */
@media (prefers-reduced-motion: no-preference) {
  /* Enable smooth scrolling animations */
  .scroll-animate {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Smooth fade-in animations */
  .fade-in {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease, transform 0.6s ease;
  }

  .fade-in.visible {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Global utility classes - keep only truly global styles */
