'use client';

import { useState, useEffect } from 'react';
import styles from './ScrollToTop.module.css';
import { ultraSmoothScrollToTop } from '@/utils/smoothScroll';

export default function ScrollToTop() {
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [isScrolling, setIsScrolling] = useState(false);

  // Show/hide scroll to top button based on scroll position
  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      setShowScrollTop(scrollPosition > 300);
      
      // Add scrolling state for enhanced animations
      setIsScrolling(true);
      clearTimeout(window.scrollTimeout);
      window.scrollTimeout = setTimeout(() => {
        setIsScrolling(false);
      }, 150);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => {
      window.removeEventListener('scroll', handleScroll);
      clearTimeout(window.scrollTimeout);
    };
  }, []);

  // Ultra-smooth scroll to top function
  const scrollToTop = () => {
    ultraSmoothScrollToTop(1800); // Ultra-smooth 1.8 second animation
  };

  if (!showScrollTop) return null;

  return (
    <button
      onClick={scrollToTop}
      className={`${styles.scrollToTop} ${isScrolling ? styles.scrolling : ''}`}
      aria-label="Scroll to top"
      title="Scroll to top"
    >
      <i className="fa-solid fa-chevron-up" style={{fontStyle: 'normal'}}></i>
      <div className={styles.ripple}></div>
    </button>
  );
}

// Extend Window interface for TypeScript
declare global {
  interface Window {
    scrollTimeout: NodeJS.Timeout;
  }
}
